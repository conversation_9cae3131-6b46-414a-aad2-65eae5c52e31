'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BookOpen,
  Search,
  Play,
  CheckCircle,
  Clock,
  Award
} from 'lucide-react';
import Link from 'next/link';

export default function StudentCoursesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [enrollmentCode, setEnrollmentCode] = useState('');

  // Mock data - in real app, this would come from API
  const enrolledCourses = [
    {
      id: 1,
      name: 'Introduction to Algebra',
      description: 'Basic algebraic concepts and problem solving',
      type: 'self_paced',
      courseCode: 'MATH101',
      progress: 85,
      totalModules: 8,
      completedModules: 7,
      nextChapter: 'Chapter 8: Final Review',
      dueDate: '2024-12-15',
      status: 'in_progress'
    },
    {
      id: 2,
      name: 'Physics Fundamentals',
      description: 'Introduction to basic physics principles',
      type: 'verified',
      courseCode: 'PHYS101',
      progress: 45,
      totalModules: 12,
      completedModules: 5,
      nextChapter: 'Chapter 6: Energy and Work',
      dueDate: '2024-11-30',
      status: 'in_progress'
    },
    {
      id: 3,
      name: 'Chemistry Basics',
      description: 'Fundamental chemistry concepts',
      type: 'self_paced',
      courseCode: 'CHEM101',
      progress: 100,
      totalModules: 6,
      completedModules: 6,
      nextChapter: null,
      dueDate: '2024-10-20',
      status: 'completed'
    }
  ];

  const availableCourses = [
    {
      id: 4,
      name: 'Advanced Mathematics',
      description: 'Calculus and advanced mathematical concepts',
      type: 'verified',
      courseCode: 'MATH201',
      instructor: 'Dr. Smith',
      duration: '12 weeks',
      difficulty: 'Advanced'
    },
    {
      id: 5,
      name: 'Biology Fundamentals',
      description: 'Introduction to biological sciences',
      type: 'self_paced',
      courseCode: 'BIO101',
      instructor: 'Prof. Johnson',
      duration: '8 weeks',
      difficulty: 'Beginner'
    }
  ];

  const handleEnrollWithCode = async () => {
    if (!enrollmentCode.trim()) return;

    try {
      // TODO: Implement API call to enroll with code
      console.log('Enrolling with code:', enrollmentCode);
      setEnrollmentCode('');
      // Show success message
    } catch (error) {
      console.error('Enrollment error:', error);
    }
  };

  const filteredEnrolledCourses = enrolledCourses.filter(
    (course) =>
      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredAvailableCourses = availableCourses.filter(
    (course) =>
      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>
          <p className='text-muted-foreground'>
            Access your enrolled courses and discover new ones
          </p>
        </div>
      </div>

      {/* Quick Enrollment */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Enrollment</CardTitle>
          <CardDescription>
            Enter a course code to quickly enroll in a course
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex space-x-2'>
            <Input
              placeholder='Enter course code (e.g., MATH101)'
              value={enrollmentCode}
              onChange={(e) => setEnrollmentCode(e.target.value.toUpperCase())}
              className='flex-1'
            />
            <Button
              onClick={handleEnrollWithCode}
              disabled={!enrollmentCode.trim()}
            >
              Enroll
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue='enrolled' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='enrolled'>My Courses</TabsTrigger>
          <TabsTrigger value='available'>Available Courses</TabsTrigger>
        </TabsList>

        <div className='flex items-center space-x-2'>
          <div className='relative flex-1'>
            <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
            <Input
              placeholder='Search courses...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-8'
            />
          </div>
        </div>

        <TabsContent value='enrolled'>
          <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
            {filteredEnrolledCourses.map((course) => (
              <Card key={course.id} className='flex flex-col'>
                <CardHeader>
                  <div className='flex items-start justify-between'>
                    <div className='space-y-1'>
                      <CardTitle className='text-lg'>{course.name}</CardTitle>
                      <code className='bg-muted rounded px-2 py-1 text-sm'>
                        {course.courseCode}
                      </code>
                    </div>
                    <Badge
                      variant={
                        course.type === 'verified' ? 'default' : 'secondary'
                      }
                    >
                      {course.type}
                    </Badge>
                  </div>
                  <CardDescription>{course.description}</CardDescription>
                </CardHeader>
                <CardContent className='flex-1 space-y-4'>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between text-sm'>
                      <span>Progress</span>
                      <span>{course.progress}%</span>
                    </div>
                    <Progress value={course.progress} className='h-2' />
                    <p className='text-muted-foreground text-xs'>
                      {course.completedModules} of {course.totalModules} modules
                      completed
                    </p>
                  </div>

                  {course.status === 'in_progress' && course.nextChapter && (
                    <div className='space-y-2'>
                      <p className='text-sm font-medium'>Next:</p>
                      <p className='text-muted-foreground text-sm'>
                        {course.nextChapter}
                      </p>
                    </div>
                  )}

                  <div className='space-y-2'>
                    <div className='text-muted-foreground flex items-center space-x-2 text-sm'>
                      <Clock className='h-4 w-4' />
                      <span>
                        Due: {new Date(course.dueDate).toLocaleDateString()}
                      </span>
                    </div>
                    <Badge
                      variant={
                        course.status === 'completed' ? 'default' : 'outline'
                      }
                    >
                      {course.status === 'completed' ? (
                        <>
                          <CheckCircle className='mr-1 h-3 w-3' />
                          Completed
                        </>
                      ) : (
                        'In Progress'
                      )}
                    </Badge>
                  </div>
                </CardContent>
                <div className='p-6 pt-0'>
                  <Link href={`/dashboard/student/courses/${course.id}`}>
                    <Button className='w-full'>
                      {course.status === 'completed' ? (
                        <>
                          <Award className='mr-2 h-4 w-4' />
                          View Certificate
                        </>
                      ) : (
                        <>
                          <Play className='mr-2 h-4 w-4' />
                          Continue Learning
                        </>
                      )}
                    </Button>
                  </Link>
                </div>
              </Card>
            ))}
          </div>

          {filteredEnrolledCourses.length === 0 && (
            <Card>
              <CardContent className='pt-6'>
                <div className='py-8 text-center'>
                  <BookOpen className='text-muted-foreground mx-auto h-12 w-12' />
                  <h3 className='mt-2 text-sm font-semibold'>
                    No enrolled courses
                  </h3>
                  <p className='text-muted-foreground mt-1 text-sm'>
                    {searchTerm
                      ? 'No courses match your search.'
                      : 'Get started by enrolling in a course.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value='available'>
          <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
            {filteredAvailableCourses.map((course) => (
              <Card key={course.id} className='flex flex-col'>
                <CardHeader>
                  <div className='flex items-start justify-between'>
                    <div className='space-y-1'>
                      <CardTitle className='text-lg'>{course.name}</CardTitle>
                      <code className='bg-muted rounded px-2 py-1 text-sm'>
                        {course.courseCode}
                      </code>
                    </div>
                    <Badge
                      variant={
                        course.type === 'verified' ? 'default' : 'secondary'
                      }
                    >
                      {course.type}
                    </Badge>
                  </div>
                  <CardDescription>{course.description}</CardDescription>
                </CardHeader>
                <CardContent className='flex-1 space-y-4'>
                  <div className='space-y-2 text-sm'>
                    <div className='flex items-center justify-between'>
                      <span className='text-muted-foreground'>Instructor:</span>
                      <span>{course.instructor}</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-muted-foreground'>Duration:</span>
                      <span>{course.duration}</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-muted-foreground'>Difficulty:</span>
                      <Badge variant='outline' className='text-xs'>
                        {course.difficulty}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
                <div className='p-6 pt-0'>
                  <Button className='w-full' variant='outline'>
                    <BookOpen className='mr-2 h-4 w-4' />
                    Request Enrollment
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          {filteredAvailableCourses.length === 0 && (
            <Card>
              <CardContent className='pt-6'>
                <div className='py-8 text-center'>
                  <BookOpen className='text-muted-foreground mx-auto h-12 w-12' />
                  <h3 className='mt-2 text-sm font-semibold'>
                    No available courses
                  </h3>
                  <p className='text-muted-foreground mt-1 text-sm'>
                    {searchTerm
                      ? 'No courses match your search.'
                      : 'Check back later for new courses.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
