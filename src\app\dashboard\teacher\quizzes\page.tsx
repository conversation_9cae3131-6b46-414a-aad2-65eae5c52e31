'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  FileText,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Clock
} from 'lucide-react';
import Link from 'next/link';

export default function QuizzesPage() {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - in real app, this would come from API
  const quizzes = [
    {
      id: 1,
      name: 'Module 1 Assessment',
      description: 'Basic concepts and fundamentals',
      course: 'Introduction to Algebra',
      chapter: 'Chapter 1: Overview',
      questionCount: 10,
      timeLimit: 30,
      minimumScore: 70,
      attempts: 25,
      averageScore: 85,
      status: 'published',
      createdAt: '2024-07-15'
    },
    {
      id: 2,
      name: 'Physics Quiz 1',
      description: "Newton's laws and motion",
      course: 'Physics Fundamentals',
      chapter: 'Chapter 2: Motion',
      questionCount: 8,
      timeLimit: 20,
      minimumScore: 75,
      attempts: 18,
      averageScore: 78,
      status: 'published',
      createdAt: '2024-07-20'
    },
    {
      id: 3,
      name: 'Chemistry Final Exam',
      description: 'Comprehensive final assessment',
      course: 'Chemistry Basics',
      chapter: null,
      questionCount: 25,
      timeLimit: 60,
      minimumScore: 80,
      attempts: 0,
      averageScore: 0,
      status: 'draft',
      createdAt: '2024-08-01'
    }
  ];

  const filteredQuizzes = quizzes.filter(
    (quiz) =>
      quiz.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quiz.course.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quiz.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Quizzes & Assessments
          </h1>
          <p className='text-muted-foreground'>
            Create and manage quizzes for your courses
          </p>
        </div>
        <Link href='/dashboard/teacher/quizzes/new'>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Create Quiz
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Quizzes</CardTitle>
          <CardDescription>
            View and manage all your quizzes and assessments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='mb-4 flex items-center space-x-2'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
              <Input
                placeholder='Search quizzes...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-8'
              />
            </div>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Quiz</TableHead>
                  <TableHead>Course/Chapter</TableHead>
                  <TableHead>Questions</TableHead>
                  <TableHead>Time Limit</TableHead>
                  <TableHead>Performance</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredQuizzes.map((quiz) => (
                  <TableRow key={quiz.id}>
                    <TableCell>
                      <div className='space-y-1'>
                        <p className='font-medium'>{quiz.name}</p>
                        <p className='text-muted-foreground text-sm'>
                          {quiz.description}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='space-y-1'>
                        <p className='text-sm font-medium'>{quiz.course}</p>
                        {quiz.chapter && (
                          <p className='text-muted-foreground text-xs'>
                            {quiz.chapter}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-1'>
                        <FileText className='text-muted-foreground h-4 w-4' />
                        <span>{quiz.questionCount}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-1'>
                        <Clock className='text-muted-foreground h-4 w-4' />
                        <span>{quiz.timeLimit}m</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='space-y-1'>
                        <p className='text-sm'>{quiz.attempts} attempts</p>
                        {quiz.attempts > 0 && (
                          <p className='text-muted-foreground text-xs'>
                            Avg: {quiz.averageScore}% (Min: {quiz.minimumScore}
                            %)
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          quiz.status === 'published' ? 'default' : 'outline'
                        }
                      >
                        {quiz.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' className='h-8 w-8 p-0'>
                            <MoreHorizontal className='h-4 w-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/quizzes/${quiz.id}`}
                            >
                              <Edit className='mr-2 h-4 w-4' />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/quizzes/${quiz.id}/results`}
                            >
                              <Eye className='mr-2 h-4 w-4' />
                              View Results
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/quizzes/${quiz.id}/preview`}
                            >
                              <FileText className='mr-2 h-4 w-4' />
                              Preview
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem className='text-red-600'>
                            <Trash2 className='mr-2 h-4 w-4' />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredQuizzes.length === 0 && (
            <div className='py-8 text-center'>
              <FileText className='text-muted-foreground mx-auto h-12 w-12' />
              <h3 className='mt-2 text-sm font-semibold'>No quizzes found</h3>
              <p className='text-muted-foreground mt-1 text-sm'>
                {searchTerm
                  ? 'Try adjusting your search terms.'
                  : 'Get started by creating a new quiz.'}
              </p>
              {!searchTerm && (
                <div className='mt-6'>
                  <Link href='/dashboard/teacher/quizzes/new'>
                    <Button>
                      <Plus className='mr-2 h-4 w-4' />
                      Create Quiz
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
